# Signal Stack Trading Platform

A scalable, modular stock market trading platform built with FastAPI, PostgreSQL, and TimescaleDB for efficient time-series data storage and analysis.

## 🚀 Features

- **Time-Series Data Storage**: PostgreSQL + TimescaleDB for efficient OHLCV data storage
- **Automatic Timeframe Conversion**: 1min → 5, 10, 15, 30, 60 min, daily
- **Strategy-Based Screener**: Background engine for symbol screening
- **Backtesting Engine**: Historical strategy testing with performance metrics
- **Paper Trading**: Virtual trading with real-time data
- **Web API**: RESTful API for data access and trading operations
- **React Frontend**: Modern UI for data visualization and strategy management

## 📋 Prerequisites

- Python 3.8+
- PostgreSQL 16+ with TimescaleDB extension
- Redis (for background tasks)
- Node.js 16+ (for frontend)

## 🛠️ Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd signal_stack
```

### 2. Setup Python Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Database Setup

#### Install PostgreSQL and TimescaleDB

1. Install PostgreSQL 16 from [official website](https://www.postgresql.org/download/)
2. Install TimescaleDB extension:
   ```bash
   # On Windows (using installer)
   # Download from https://www.timescale.com/downloads
   
   # On Ubuntu/Debian
   sudo apt install timescaledb-2-postgresql-16
   
   # On macOS
   brew install timescaledb
   ```

#### Create Database

```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database
CREATE DATABASE nse_db;

-- Create user (optional)
CREATE USER trading_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE nse_db TO trading_user;
```

### 4. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# Update database credentials, Fyers API keys, etc.
```

### 5. Initialize Database

```bash
# Run database setup script
python scripts/setup_database.py
```

## 🚀 Running the Application

### Start the API Server

```bash
# Development mode
python main.py

# Or using uvicorn directly
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Start Background Workers (Optional)

```bash
# Start Redis server
redis-server

# Start Celery worker
celery -A app.tasks.celery worker --loglevel=info
```

## 📊 Database Schema

### Core Tables

- **symbols**: Master table for trading symbols
- **stock_ohlcv**: 1-minute OHLCV data (TimescaleDB hypertable)
- **stock_ohlcv_agg**: Aggregated timeframe data
- **strategies**: Strategy definitions
- **screener_results**: Symbol screening results
- **backtest_results**: Backtesting performance metrics
- **paper_trades**: Paper trading records

### TimescaleDB Features

- Automatic partitioning by time
- Optimized for time-series queries
- Compression for historical data
- Continuous aggregates for real-time analytics

## 🔧 Configuration

### Environment Variables

```env
# Fyers API
FYERS_CLIENT_ID=your_client_id
FYERS_SECRET_KEY=your_secret_key
FYERS_ACCESS_TOKEN=your_access_token

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=nse_db
DATABASE_USER=postgres
DATABASE_PASSWORD=your_password

# Redis
REDIS_URL=redis://localhost:6379/0

# API
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
```

### YAML Configuration

The `config.yaml` file contains trading-specific settings:

- Symbol lists
- Market filters
- Technical indicator parameters
- Timeframe settings
- Rate limiting configuration

## 📈 API Endpoints

### Health Check
- `GET /` - Root endpoint
- `GET /health` - Health check

### Data Endpoints (Coming Soon)
- `GET /api/v1/symbols` - List symbols
- `GET /api/v1/ohlcv/{symbol}` - Get OHLCV data
- `GET /api/v1/strategies` - List strategies

### Trading Endpoints (Coming Soon)
- `POST /api/v1/backtest` - Run backtest
- `GET /api/v1/paper-trades` - Get paper trades
- `POST /api/v1/paper-trades` - Create paper trade

## 🧪 Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=app tests/
```

## 📁 Project Structure

```
signal_stack/
├── app/
│   ├── core/           # Core configuration and utilities
│   ├── database/       # Database models and connection
│   ├── api/           # API endpoints (coming soon)
│   ├── services/      # Business logic services (coming soon)
│   └── tasks/         # Background tasks (coming soon)
├── scripts/           # Utility scripts
├── tests/            # Test files
├── frontend/         # React frontend (coming soon)
├── config.yaml       # Trading configuration
├── requirements.txt  # Python dependencies
└── main.py          # Application entry point
```

## 🔄 Development Roadmap

- [x] Project setup and database infrastructure
- [ ] Data storage module implementation
- [ ] Fyers API integration
- [ ] Timeframe aggregation engine
- [ ] Technical indicators module
- [ ] Strategy-based screener
- [ ] Backtesting module
- [ ] Paper trading module
- [ ] Web API backend
- [ ] React frontend UI
- [ ] Configuration management system
- [ ] Testing and integration

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For support and questions, please open an issue in the repository.
